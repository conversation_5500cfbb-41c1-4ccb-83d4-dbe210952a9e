import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Header from "@/components/Header";
import Image from "next/image";
import { cn, racing, spicy } from "@/lib/utils";

export default function WelcomeScreen() {
  return (
    <div>
      <Header />

      <Image
        src={"/bg1.png"}
        alt=""
        width={600}
        height={100}
        className="absolute top-3 left-0 w-full object-cover"
      />
      <Image
        src={"/bg2.png"}
        alt=""
        width={600}
        height={100}
        className="absolute top-[330px] md:top-[400px] lg:top-[500px] left-0 w-full object-cover"
      />

      {/* Main Content */}
      <div className="relative z-10 p-6 lg:p-12 xl:p-16">
        {/* Welcome Banner */}
        <div className="relative mb-20 lg:mb-32 xl:mb-40">
          <Image
            src={"/Group 48096007.png"}
            alt=""
            width={800}
            height={600}
            className="absolute -top-8 lg:-top-12 xl:-top-24 w-full px-10 lg:px-12 xl:px-16 max-w-3xl left-1/2 -translate-x-1/2"
          />

          <div className="text-center relative z-10 mt-6 lg:mt-12 xl:mt-16">
            <h1 className="mb-2 tracking-widest text-sm md:text-base lg:text-4xl">
              WELCOME TO
              <br />
              <span
                className={cn(
                  "text-[44px] md:text-6xl lg:text-8xl tracking-normal leading-6 md:leading-8 lg:leading-10 xl:leading-14",
                  racing.className
                )}
              >
                GULULAND
              </span>
            </h1>
            <p
              className={cn(
                "text-[10px] md:text-xs lg:text-sm xl:text-lg text-white font-black mb-1 lg:mb-2 xl:mb-3",
                spicy.className
              )}
            >
              Burn Your Ridiculous Dragons And Nomaimai
              <br />
              NFTs For Gululu Points
            </p>
            <Button className="bg-[#FFB101] scale-90 lg:scale-100 xl:scale-110 text-[10px] md:text-xs lg:text-sm xl:text-lg h-6 md:h-8 lg:h-10 xl:h-12 rounded-full px-4 lg:px-6 xl:px-8">
              Connect Wallet
            </Button>
          </div>
        </div>

        {/* Process Steps */}
        <Image
          src={"/cardbg.png"}
          alt=""
          width={600}
          height={100}
          className="mb-4 absolute left-0 z-[-1] w-full"
        />
        <div className="space-y-2.5 lg:space-y-4 xl:space-y-6 w-full pt-4 lg:pt-8 xl:pt-12 z-10 relative max-w-4xl mx-auto">
          <Image
            src={"/12.png"}
            alt=""
            width={160}
            height={100}
            className="mb-4 absolute -translate-y-full top-4 lg:top-8 xl:top-12 -right-10 lg:-right-16 xl:-right-36 z-[1] w-36 md:w-40 lg:w-56 xl:w-92"
          />
          <Image
            src={"/32.png"}
            alt=""
            width={150}
            height={100}
            className="mb-4 absolute -translate-y-full top-8 lg:top-12 xl:top-16 -left-9 lg:-left-16 xl:-left-36 z-[1] w-36 md:w-38 lg:w-56 xl:w-92"
          />

          <Card className="border-2 lg:border-4 p-0 mx-3 lg:mx-6 xl:mx-8 rounded-none border-black bg-[#FFC67C] shadow-neo">
            <CardContent className="px-2 lg:px-4 xl:px-12 py-2 lg:py-4 xl:py-6 flex items-center gap-4 lg:gap-6 xl:gap-8">
              <div className="flex-1">
                <h3 className="font-bold text-black text-sm lg:text-xl xl:text-3xl">
                  Connect Wallet
                </h3>
                <p className="text-[10px] lg:text-sm xl:text-lg leading-tight text-black">
                  Connect your Web3 wallet to
                  <br />
                  access your NFT collection.
                </p>
              </div>
              <Image
                src={"/Untitled-1 1.png"}
                alt=""
                width={70}
                className="w-20 lg:w-28 xl:w-36"
                height={50}
              />
            </CardContent>
          </Card>

          <Card className="border-2 lg:border-4 p-0 mx-3 lg:mx-6 xl:mx-8 rounded-none border-black bg-[#FFC67C] shadow-neo">
            <CardContent className="px-2 lg:px-4 xl:px-12 py-2 lg:py-4 xl:py-6 flex items-center gap-4 lg:gap-6 xl:gap-8">
              <Image
                src={"/Group 48096005.png"}
                alt=""
                width={70}
                className="w-20 lg:w-28 xl:w-36 -translate-y-1"
                height={50}
              />
              <div className="flex-1 text-right">
                <h3 className="font-bold text-black text-sm lg:text-xl xl:text-3xl">
                  Select NFT
                </h3>
                <p className="text-[10px] lg:text-sm xl:text-lg leading-tight text-black">
                  Choose an NFT from your collection
                  <br />
                  that you want to burn.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="border-2 lg:border-4 py-1 lg:py-2 xl:py-3 mx-3 lg:mx-6 xl:mx-8 rounded-none border-black bg-[#FFC67C] shadow-neo">
            <CardContent className="px-2 lg:px-4 xl:px-12 py-1 lg:py-3 xl:py-5 flex items-center gap-4 lg:gap-6 xl:gap-8">
              <div className="flex-1">
                <h3 className="font-bold text-black text-sm lg:text-xl xl:text-3xl">
                  Burn & Analyse
                </h3>
                <p className="text-[10px] lg:text-sm xl:text-lg leading-tight text-black">
                  Burn the NFT and we analyse its
                  <br />
                  metadata for rarity.
                </p>
              </div>
              <Image
                src={"/Group 48096003.png"}
                alt=""
                width={70}
                className="w-20 lg:w-28 xl:w-36"
                height={50}
              />
            </CardContent>
          </Card>

          <Card className="border-2 lg:border-4 py-2 lg:py-4 xl:py-6 mx-3 lg:mx-6 xl:mx-8 rounded-none border-black bg-[#FFC67C] shadow-neo">
            <CardContent className="px-2 lg:px-4 xl:px-12 py-1 lg:py-3 xl:py-5 flex items-center gap-4 lg:gap-6 xl:gap-8">
              <Image
                src={"/Group 48096004.png"}
                alt=""
                width={70}
                className="w-20 lg:w-28 xl:w-36"
                height={50}
              />
              <div className="flex-1 text-right">
                <h3 className="font-bold text-black text-sm lg:text-xl xl:text-3xl">
                  Earn Points
                </h3>
                <p className="text-[10px] lg:text-sm xl:text-lg leading-tight text-black">
                  Receive points based on the rarity
                  <br />
                  and view in your dashboard.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
