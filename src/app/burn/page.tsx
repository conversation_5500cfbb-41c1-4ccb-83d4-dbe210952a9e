import Header from "@/components/Header";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn, racing, spicy } from "@/lib/utils";
import { Star } from "lucide-react";
import Image from "next/image";

export default function BurnNftsScreen() {
  const nftCards = Array.from({ length: 6 }, (_, i) => ({
    id: i + 1,
    name: "CARD NAME",
    price: "15.99",
  }));

  return (
    <>
      <Header />
      <div className="relative z-10 p-6 pb-20">
        <div className="mb-6">
          <p className="text-black text-sm mb-2">CASH OUT</p>
          <h1
            className={cn(
              "text-4xl leading-8 font-black text-black mb-3",
              racing.className
            )}
          >
            burn nfts
          </h1>
          <p className={cn("text-[10px] text-black mb-4", spicy.className)}>
            Burn Your Ridiculous Dragons And Nomaimai
            <br />
            NFTs For Gululu Points
          </p>
        </div>

        <Image
          src={"/5 7.png"}
          alt=""
          width={400}
          height={600}
          className="absolute top-10 right-0 w-40"
        />
        {/* <Image
          src={"/cardbg.png"}
          alt=""
          width={800}
          height={600}
          className="absolute left-0 mt-32"
        /> */}

        <div className="relative mt-40 pb-10 pt-4 border-2 border-black shadow-neo px-2 bg-[#ffd6a0]">
          <Image
            src={"/4 851118.png"}
            alt=""
            width={800}
            height={600}
            className="absolute top-5 -translate-y-full z-10 w-42 -left-3"
          />

          <h2
            className={cn(
              "text-2xl text-center font-bold text-black mb-3 italic",
              racing.className
            )}
          >
            Explore inventory
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 md:gap-3 gap-4 mb-4 md:px-1 px-4">
            {nftCards.map((card) => (
              <Card
                key={card.id}
                className="border-2 border-black bg-gradient-to-b from-yellow-400 to-orange-400 shadow-neo rounded-none p-0 h-60 md:h-40 w-full hover:scale-105 transition-all duration-300"
              >
                <CardContent className="relative p-1 h-full flex flex-col overflow-hidden">
                  <div className="absolute top-0 right-0 text-lg leading-none">
                    <Star className="fill-black stroke-black size-3" />
                  </div>

                  <div className="flex-1 flex items-center justify-center px-1 absolute top-14 -left-7 transform -rotate-90 text-base md:text-sm font-bold text-black whitespace-nowrap tracking-tight">
                    {card.name}
                  </div>
                  <div className="w-64 top-32 md:-left-24 -left-20 h-0.5 bg-black absolute rotate-90"></div>

                  <div className="text-right mt-auto flex items-end flex-col gap-1.5 md:gap-1">
                    <p className="md:text-xs text-sm text-black font-medium tracking-tight leading-none">
                      PRICE: {card.price}
                    </p>
                    <Button
                      size="sm"
                      className="bg-white hover:bg-gray-100 text-black border border-black font-bold text-sm md:text-[10px] py-1 px-4 md:px-2 md:h-6 rounded-sm shadow-sm w-fit"
                    >
                      BURN
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center pt-4">
            <Button className="bg-[#FBAC82] hover:bg-[#ffbb97] h-8 rounded-full shadow-neo border border-black">
              <span className="mr-2">
                <Star className="inline-block w-3 h-3 fill-black stroke-black" />
              </span>
              <span className={cn("text-xl text-black", racing.className)}>
                VIEW MORE
              </span>
              <span className="ml-2">
                <Star className="inline-block w-3 h-3 fill-black stroke-black" />
              </span>
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
