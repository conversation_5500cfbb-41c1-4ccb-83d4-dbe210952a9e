import type { Metada<PERSON> } from "next";
import "./globals.css";
import { cn, poppins } from "@/lib/utils";
import Footer from "@/components/Footer";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={cn(
          "antialiased bg-gradient-to-b from-[#FFC882] to-[#FDE0B9]",
          poppins.className
        )}
      >
        <div className="mx-auto min-h-screen bg-[#FFC882] relative overflow-hidden max-w-6xl lg:max-w-7xl">
          {children}
          <Footer />
        </div>
      </body>
    </html>
  );
}
