import Image from "next/image";
import { Button } from "./ui/button";
import { Wallet } from "lucide-react";
import Link from "next/link";

const Header = () => {
  return (
    <div className="z-50 flex items-center justify-between py-2 px-4 bg-gradient-to-r from-[#FFB100] to-[#E29D00] border-2 border-amber-800/60 rounded-lg mx-2 mt-3 relative">
      <Image
        src={"/Group 48095938.png"}
        alt=""
        width={20}
        height={50}
        className="absolute top-1/2 -translate-y-1/2 -left-2"
      />
      <Image
        src={"/Group 48095938.png"}
        alt=""
        width={20}
        height={50}
        className="absolute top-1/2 -translate-y-1/2 -right-2"
      />
      <Link href={"/"} className="flex items-center gap-2">
        <Image src={"/logo.png"} alt="" width={100} height={150} />
      </Link>
      <div className="flex gap-2">
        {/* <div className="flex items-center gap-1 bg-orange-500 border-2 border-black rounded px-2 py-1">
          <Flame className="w-4 h-4 text-white" />
          <span className="text-white font-bold text-sm">20</span>
        </div>
        <div className="flex items-center gap-1 bg-orange-500 border-2 border-black rounded px-2 py-1">
          <Wallet className="w-4 h-4 text-white" />
          <span className="text-white font-bold text-sm">0.2094</span>
        </div> */}
        <Button
          className="bg-[#e9b234] text-white font-semibold text-sm border-[#b68a24c9] border-2 tracking-tight"
          size={"sm"}
        >
          <Wallet className="w-4 h-4 mr-1" />
          Connect Wallet
        </Button>
      </div>
    </div>
  );
};

export default Header;
